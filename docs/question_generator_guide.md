# 测试问题生成器使用指南

## 概述

测试问题生成器是CBAO自动化测试工具的新功能，可以根据配置的维度和指标自动生成各种类型的测试问题。现在还支持使用大模型筛查优化生成的问题。

## 功能特性

### 支持的问题类型

#### 1. 指标达成类型
- **无时间查询**: 维度+指标的基础查询
  - 示例: "抖音群组的审核量？"
- **单时间点查询**: 特定时间点的查询
  - 示例: "昨天抖音群组的审核量？"
- **时间范围查询**: 时间段内的查询
  - 示例: "本月内抖音群组的审核量？"
- **时间对比查询**: 不同时间的对比
  - 示例: "抖音群组的审核量，本月与上月对比？"
- **维度对比查询**: 不同维度的对比
  - 示例: "抖音群组与直播群组的审核量对比？"
- **维度计算聚合**: 按维度聚合计算
  - 示例: "按群组统计审核量的总和？"
- **时间计算聚合**: 按时间聚合计算
  - 示例: "按月统计抖音群组的审核量平均值？"
- **多指标查询**: 同时查询多个指标
  - 示例: "抖音群组的审核量和准确率？"

#### 2. 趋势查询类型
- **时间范围查询**: 时间段内的趋势
  - 示例: "本月内抖音群组的审核量趋势？"
- **时间对比查询**: 不同时间段的趋势对比
  - 示例: "抖音群组的审核量趋势，本月与上月对比？"

#### 3. 下钻查询类型
- **单层下钻**: 从上级维度下钻到下级
  - 示例: "商业生态与安全(商安)下各群组的审核量？"
- **多层下钻**: 多层级的下钻查询
  - 示例: "商业生态与安全(商安)下按群组和业务的审核量？"

### 新功能：大模型问题筛查优化

大模型问题筛查优化功能可以使用大模型对自动生成的问题进行筛查和优化，确保问题：
- 语句通顺、表达清晰
- 符合中文语法习惯
- 专业性强，适合数据分析场景
- 保持问题的原始意图和类型不变

## 使用方法

### 1. 命令行使用

#### 显示问题生成示例
```bash
python main.py --mode example
```

#### 生成问题到控制台
```bash
python main.py --mode generate --count 3
```

#### 使用大模型筛查优化问题
```bash
# 使用默认提示词
python main.py --mode generate --count 3 --use-llm-filter

# 使用自定义提示词
python main.py --mode generate --count 3 --use-llm-filter --filter-prompt "请优化这些问题，使其更专业"
```

#### 生成问题到文件
```bash
# 不使用大模型筛查
python main.py --mode generate --count 5 --output generated_questions.md

# 使用大模型筛查
python main.py --mode generate --count 5 --output optimized_questions.md --use-llm-filter
```

#### 生成问题并插入到飞书表格
```bash
# 不使用大模型筛查
python main.py --mode insert-feishu --count 3

# 使用大模型筛查
python main.py --mode insert-feishu --count 3 --use-llm-filter
```

#### 运行生成的问题测试
```bash
python main.py --mode run-generated --count 3
```

#### 运行原有的飞书表格测试
```bash
python main.py --mode test
```

### 2. 编程接口使用

```python
from core.question_generator import QuestionGenerator
from core.llm_filter import LLMQuestionFilter
from utils.config_loader import config

# 初始化生成器
generator = QuestionGenerator()

# 生成特定类型的问题
no_time_questions = generator.generate_no_time_query(5)
trend_questions = generator.generate_trend_time_range_query(3)

# 生成所有类型的问题
all_questions = generator.generate_all_question_types(count_per_type=3)

# 使用大模型筛查优化问题
question_filter = LLMQuestionFilter(config)
optimized_questions = question_filter.filter_questions(all_questions)

# 使用自定义提示词
custom_prompt = """
请对以下问题进行优化，使其更加专业、清晰，并符合数据分析领域的表达习惯。
保持问题的原始意图不变，但可以调整表达方式使其更加准确。
"""
optimized_questions = question_filter.filter_questions(all_questions, custom_prompt)

# 获取扁平化的问题列表
flat_questions = generator.get_flat_question_list(count_per_type=2)

# 生成问题到文件
generator.generate_questions_to_file("my_questions.md", count_per_type=5)

# 保存已筛查优化的问题到文件
generator.save_questions_to_file(optimized_questions, "optimized_questions.md")
```

## 配置说明

### 问题生成器配置
问题生成器使用 `config/renli_table.yaml` 文件中的配置：

### 大模型筛查配置
大模型筛查功能使用 `config/config.yaml` 中的 `llm` 配置：

```yaml
llm:
  api_key: your_api_key
  base_url: https://api.example.com
  model_name: model_name
```

### 维度配置
```yaml
维度:
  中心:
    - 商业生态与安全(商安)
    - 内容质量中心(内安)
    - 运营支持中心(运支)
  群组:
    - 广告群组(广告)
    - 直播群组(直播)
    - 抖音群组
  # ... 更多维度
```

### 指标配置
```yaml
指标:
  审核量:
    - 日审核量
    - 周审核量
    - 月审核量
  准确率:
    - 审核准确率
    - 质检准确率
  # ... 更多指标
```

### 限制配置
```yaml
限制:
  时间范围:
    - 今天
    - 昨天
    - 本周
    - 本月
  聚合方式:
    - 求和
    - 平均值
    - 最大值
  # ... 更多限制
```

## 自定义扩展

### 添加新的问题模板

在 `QuestionGenerator` 类的 `question_templates` 字典中添加新模板：

```python
self.question_templates['new_type'] = [
    "新模板1: {dimension}的{metric}如何？",
    "新模板2: 查看{dimension}在{metric}方面的表现"
]
```

### 自定义大模型筛查提示词

可以通过命令行参数或编程接口自定义大模型筛查的提示词：

```python
# 命令行方式
python main.py --mode generate --count 3 --use-llm-filter --filter-prompt "自定义提示词"

# 编程接口方式
custom_prompt = "自定义提示词"
optimized_questions = question_filter.filter_questions(all_questions, custom_prompt)
```

### 添加新的维度或指标

直接修改 `config/renli_table.yaml` 文件，添加新的维度或指标配置。

### 自定义生成逻辑

继承 `QuestionGenerator` 类并重写相关方法：

```python
class CustomQuestionGenerator(QuestionGenerator):
    def generate_custom_query(self, count: int = 5) -> List[str]:
        # 自定义生成逻辑
        pass
```

## 注意事项

1. **配置文件格式**: 确保 `renli_table.yaml` 文件格式正确，使用YAML语法
2. **编码问题**: 配置文件和生成的问题都使用UTF-8编码
3. **问题质量**: 生成的问题基于配置的维度和指标，质量取决于配置的完整性
4. **大模型API**: 使用大模型筛查功能需要正确配置API密钥和URL
5. **性能考虑**: 大量生成问题并使用大模型筛查时，处理时间会增加
6. **测试环境**: 运行生成的问题测试需要配置好相应的测试环境

## 故障排除

### 常见问题

1. **配置文件加载失败**
   - 检查文件路径是否正确
   - 确认YAML语法是否正确

2. **生成的问题不符合预期**
   - 检查维度和指标配置是否完整
   - 确认问题模板是否合适

3. **大模型筛查失败**
   - 检查API密钥和URL配置是否正确
   - 确认网络连接是否正常
   - 查看日志了解详细错误信息

4. **运行测试时出错**
   - 确认测试环境配置正确
   - 检查网络连接和权限设置

### 调试方法

启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 更新日志

- v1.1.0: 新增大模型问题筛查优化功能
- v1.0.0: 初始版本，支持12种问题类型生成
- 支持命令行和编程接口
- 集成到主测试流程
