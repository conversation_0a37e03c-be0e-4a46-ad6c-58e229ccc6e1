import asyncio
import os
from .screenshot_taker import ScreenshotTaker
from .feishu_manager import FeishuManager

class TestRunner:
    """
    负责执行整个测试流程，包括截图和上传结果。
    """
    def __init__(self, config):
        """
        初始化 TestRunner。

        Args:
            config (dict): 包含所有配置信息的字典。
        """
        self.config = config
        self.feishu_manager = FeishuManager(config)
        self.screenshot_dir = os.path.join(os.path.dirname(__file__), '..', 'screenshots')
        os.makedirs(self.screenshot_dir, exist_ok=True)

    async def run_tests(self):
        """
        异步执行所有测试用例。
        """
        sheet_data = self.feishu_manager.get_sheet_data()
        if not sheet_data:
            print("未能获取到飞书表格数据，或数据为空。")
            return

        print(f"成功读取 {len(sheet_data)} 条测试数据")
        print(f"截图将保存至: {self.screenshot_dir}")

        async with ScreenshotTaker(self.config) as screenshot_taker:
            tasks = []
            for original_row, row_data in sheet_data:
                question = row_data[0]
                if not question.strip():
                    continue
                task = self.process_question(screenshot_taker, original_row, question)
                tasks.append(task)
            
            await asyncio.gather(*tasks)

        print("所有问题处理完成")

    async def process_question(self, screenshot_taker: ScreenshotTaker, original_row: int, question: str):
        """
        处理单个问题：截图并上传结果。

        Args:
            screenshot_taker (ScreenshotTaker): 用于截图的实例。
            original_row (int): 问题在表格中的原始行号。
            question (str): 要测试的问题。
        """
        try:
            # 文件名处理，替换不安全字符
            safe_filename = "".join(c if c.isalnum() or c in (' ', '_') else '_' for c in question).rstrip()
            screenshot_filename = f"{safe_filename}.png"
            screenshot_path = os.path.join(self.screenshot_dir, screenshot_filename)

            await screenshot_taker.take_screenshot(question, screenshot_path)
            self.feishu_manager.upload_image_and_update_sheet(screenshot_path, original_row)

        except Exception as e:
            print(f"处理问题 '{question}' 时出错: {e}")