import random
import yaml
from typing import Dict, List, Any, Tuple
from pathlib import Path
import logging
from utils.config_loader import ConfigLoader

class QuestionGenerator:
    """
    测试问题生成器，根据维度和指标生成不同类型的测试问题
    """
    
    def __init__(self, config_path: str = None):
        """
        初始化问题生成器
        
        Args:
            config_path (str): renli_table.yaml配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        
        if config_path is None:
            # 使用默认路径
            project_root = Path(__file__).parent.parent
            config_path = project_root / 'config' / 'renli_table.yaml'
        
        self.config_path = Path(config_path)
        self.config_data = self._load_config()
        
        # 问题模板定义
        self.question_templates = {
            # 指标达成类型
            'no_time_query': [
                "{dimension}的{metric}？",
                "{dimension}的{metric}是多少？",
                "查询{dimension}的{metric}",
                "{dimension}{metric}情况如何？"
            ],
            'single_time_query': [
                "{time_point}{dimension}的{metric}？",
                "{time_point}{dimension}的{metric}是多少？",
                "查询{time_point}{dimension}的{metric}",
                "{time_point}{dimension}{metric}情况如何？"
            ],
            'time_range_query': [
                "{time_range}{dimension}的{metric}？",
                "{time_range}{dimension}的{metric}是多少？",
                "统计{time_range}{dimension}的{metric}"
            ],
            'time_comparison_query': [
                "{dimension}的{metric}，{time1}与{time2}对比？",
                "对比{time1}和{time2}{dimension}的{metric}",
                "{dimension}{metric}在{time1}和{time2}的差异？",
                "{time1}相比{time2}，{dimension}的{metric}变化如何？"
            ],
            'dimension_comparison_query': [
                "{dimension1}与{dimension2}的{metric}对比？",
                "对比{dimension1}和{dimension2}的{metric}",
                "{dimension1}和{dimension2}哪个{metric}更高？",
                "{metric}方面，{dimension1}与{dimension2}的差异？"
            ],
            'dimension_aggregation_query': [
                "按{dimension}统计{metric}的{aggregation}？",
                "{dimension}维度下{metric}的{aggregation}是多少？",
                "计算各{dimension}的{metric}{aggregation}",
                "{dimension}分组的{metric}{aggregation}情况",
                "{dimension}下{metric}的{aggregation}是多少？",
                "各{dimension}的{metric}降序排序？",
                "各{dimension}的{metric}升序排序？"
            ],
            'time_aggregation_query': [
                #"按{time_unit}统计{dimension}的{metric}{aggregation}？",
                #"{dimension}的{metric}按{time_unit}的{aggregation}趋势？",
                #"计算{dimension}{metric}的{time_unit}{aggregation}",
                #"{time_unit}维度下{dimension}{metric}的{aggregation}"
                "{time_range}{dimension}的日均{metric}是多少？"
            ],
            'multi_metric_query': [
                "{time_point}{dimension}的{metric1}和{metric2}？",
                "查询{time_point}{dimension}的{metric1}、{metric2}情况",
                "{time_point}{dimension}在{metric1}和{metric2}方面的表现？",
                "统计{time_point}{dimension}的{metric1}及{metric2}"
            ],
            # 趋势查询类型
            'trend_time_range': [
                "{time_range}{dimension}的{metric}趋势？",
                "{dimension}的{metric}在{time_range}的变化趋势？",
                "分析{time_range}{dimension}{metric}的走势"
            ],
            'trend_time_comparison': [
                "{dimension}的{metric}趋势，本周与上周对比？",
                "对比本周和上周{dimension}{metric}的趋势变化",
                "{dimension}{metric}在本周和上周的趋势差异？",
                "分析{dimension}{metric}从上周到本周的趋势",
                "{dimension}的{metric}趋势，本月与上月对比？",
                "对比本月和上月{dimension}{metric}的趋势变化",
                "{dimension}{metric}在本月和上月的趋势差异？",
                "分析{dimension}{metric}从上月到本月的趋势",
            ],
            # 下钻查询类型
            'single_drill_down': [
                "{parent_dimension}下各{child_dimension}的{metric}?",
                "按{child_dimension}细分{parent_dimension}的{metric}",
                "{parent_dimension}中{child_dimension}维度的{metric}分布？"
            ],
            'multi_drill_down': [
                "{time_point}{parent_dimension}{child_dimension1}的{metric}",
                #"{time_point}{parent_dimension}{child_dimension1}按{child_dimension2}区分的{metric1}和{metric2}是多少？",
                #"{time_point}{parent_dimension}{child_dimension1}{child_dimension2}的{metric}？",
                "{time_point}{parent_dimension}{child_dimension1}按用工模式区分的{metric}",
            ]
        }
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _get_random_item(self, items: List[str]) -> str:
        """从列表中随机选择一个项目"""
        item = random.choice(items)
        if "(" in item:
            if random.random()<0.5:
                item = item.split("(")[0]
            else:
                item = item.split("(")[1][:-1]
        return item
    
    def _get_dimension_value(self, dimension_key: str) -> str:
        """获取维度的随机值"""
        dimensions = self.config_data.get('维度', {})
        if dimension_key in dimensions:
            values = dimensions[dimension_key]
            if isinstance(values, list):
                return self._get_random_item(values)
            elif isinstance(values, str):
                return values
        return dimension_key
    
    def _get_metric_value(self, metric_key: str = None) -> str:
        """获取指标的随机值"""
        metrics = self.config_data.get('指标', {})
        if metric_key and metric_key in metrics:
            values = metrics[metric_key]
            if isinstance(values, list):
                return self._get_random_item(values)
            elif isinstance(values, str):
                return values
        else:
            # 随机选择一个指标类别
            if metrics:
                category = random.choice(list(metrics.keys()))
                values = metrics[category]
                if isinstance(values, list):
                    return self._get_random_item(values)
                elif isinstance(values, str):
                    return values
        return "审核量"
    
    def _get_time_value(self, time_type: str = "time_range") -> str:
        """获取时间相关的随机值，完全从配置文件中读取"""
        limits = self.config_data.get('限制', {})

        if time_type == "time_range":
            time_ranges = limits.get('时间范围', [])
            return self._get_random_item(time_ranges) if time_ranges else "本月"
        elif time_type == "time_point":
            time_points = limits.get('时间点', [])
            return self._get_random_item(time_points) if time_points else "昨天"
        elif time_type == "time_unit":
            time_units = limits.get('时间单位', [])
            return self._get_random_item(time_units) if time_units else "月"

        # 默认返回时间范围
        time_ranges = limits.get('时间范围', [])
        return self._get_random_item(time_ranges) if time_ranges else "本月"
    
    def _get_aggregation_value(self) -> str:
        """获取聚合方式的随机值"""
        limits = self.config_data.get('限制', {})
        aggregations = limits.get('聚合方式', [])
        return self._get_random_item(aggregations) if aggregations else "总和"

    def generate_no_time_query(self, count: int = 5) -> List[str]:
        """生成无时间查询问题"""
        questions = []
        templates = self.question_templates['no_time_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()

            question = template.format(dimension=dimension, metric=metric)
            questions.append(question)

        return questions

    def generate_single_time_query(self, count: int = 5) -> List[str]:
        """生成单时间点查询问题"""
        questions = []
        templates = self.question_templates['single_time_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()
            time_point = self._get_time_value("time_point")

            question = template.format(dimension=dimension, metric=metric, time_point=time_point)
            questions.append(question)

        return questions

    def generate_time_range_query(self, count: int = 5) -> List[str]:
        """生成时间范围查询问题"""
        questions = []
        templates = self.question_templates['time_range_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()
            time_range = self._get_time_value("time_range")

            question = template.format(dimension=dimension, metric=metric, time_range=time_range)
            questions.append(question)

        return questions

    def generate_time_comparison_query(self, count: int = 5) -> List[str]:
        """生成时间对比查询问题"""
        questions = []
        templates = self.question_templates['time_comparison_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()
            time1 = self._get_time_value("time_point")
            time2 = self._get_time_value("time_point")

            # 确保两个时间不同
            while time1 == time2:
                time2 = self._get_time_value("time_point")

            question = template.format(dimension=dimension, metric=metric, time1=time1, time2=time2)
            questions.append(question)

        return questions

    def generate_dimension_comparison_query(self, count: int = 5) -> List[str]:
        """生成维度对比查询问题"""
        questions = []
        templates = self.question_templates['dimension_comparison_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension_keys = list(self.config_data.get('维度', {}).keys())

            # 选择同一维度类型的不同值进行对比
            dimension_key = random.choice(dimension_keys)
            dimension_values = self.config_data.get('维度', {}).get(dimension_key, [])
            while(dimension_key == "CQC"):
                dimension_key = random.choice(dimension_keys)
               
            
            if isinstance(dimension_values, list) and len(dimension_values) >= 2:
                dimension1, dimension2 = random.sample(dimension_values, 2)
                if '(' in dimension1:
                    if random.random()<0.5:
                        dimension1 = dimension1.split("(")[0]
                    else:
                        dimension1 = dimension1.split("(")[1][:-1]
                if '(' in dimension2:
                    if random.random()<0.5:
                        dimension2 = dimension2.split("(")[0]
                    else:
                        dimension2 = dimension2.split("(")[1][:-1]

            metric = self._get_metric_value()

            question = template.format(dimension1=dimension1, dimension2=dimension2, metric=metric)
            questions.append(question)

        return questions

    def generate_dimension_aggregation_query(self, count: int = 5) -> List[str]:
        """生成维度计算聚合查询问题"""
        questions = []
        templates = self.question_templates['dimension_aggregation_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = random.choice(list(self.config_data.get('维度', {}).keys()))
            while(dimension == "CQC"):
                dimension = random.choice(list(self.config_data.get('维度', {}).keys()))
            metric = self._get_metric_value()
            aggregation = self._get_aggregation_value()

            question = template.format(dimension=dimension, metric=metric, aggregation=aggregation)
            questions.append(question)

        return questions

    def generate_time_aggregation_query(self, count: int = 5) -> List[str]:
        """生成时间计算聚合查询问题"""
        questions = []
        templates = self.question_templates['time_aggregation_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()
            aggregation = self._get_aggregation_value()
            time_unit = self._get_time_value("time_unit")
            time_range = self._get_time_value("time_range")

            question = template.format(dimension=dimension, metric=metric, aggregation=aggregation, time_range=time_range)
            questions.append(question)

        return questions

    def generate_multi_metric_query(self, count: int = 5) -> List[str]:
        """生成多指标查询问题"""
        questions = []
        templates = self.question_templates['multi_metric_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            time_point = self._get_time_value("time_point")
            # 选择两个不同的指标
            all_metrics = []
            for category_metrics in self.config_data.get('指标', {}).values():
                if isinstance(category_metrics, list):
                    all_metrics.extend(category_metrics)

            if len(all_metrics) >= 2:
                metric1, metric2 = random.sample(all_metrics, 2)
            else:
                metric1 = self._get_metric_value()
                metric2 = self._get_metric_value()
                while metric1 == metric2:
                    metric2 = self._get_metric_value()

            question = template.format(dimension=dimension, metric1=metric1, metric2=metric2, time_point=time_point)
            questions.append(question)

        return questions

    def generate_trend_time_range_query(self, count: int = 5) -> List[str]:
        """生成趋势时间范围查询问题"""
        questions = []
        templates = self.question_templates['trend_time_range']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()
            time_range = self._get_time_value("time_range")

            question = template.format(dimension=dimension, metric=metric, time_range=time_range)
            questions.append(question)

        return questions

    def generate_trend_time_comparison_query(self, count: int = 5) -> List[str]:
        """生成趋势时间对比查询问题"""
        questions = []
        templates = self.question_templates['trend_time_comparison']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()
            time1 = self._get_time_value("time_point")
            time2 = self._get_time_value("time_point")

            # 确保两个时间不同
            while time1 == time2:
                time2 = self._get_time_value("time_point")

            question = template.format(dimension=dimension, metric=metric, time1=time1, time2=time2)
            questions.append(question)

        return questions

    def generate_single_drill_down_query(self, count: int = 5) -> List[str]:
        """生成单层下钻查询问题"""
        questions = []
        templates = self.question_templates['single_drill_down']

        # 定义层级关系
        drill_down_relations = {
            '中心': '群组',
            '群组': '业务',
            '省份': '城市',
        }

        for _ in range(count):
            template = self._get_random_item(templates)

            # 选择一个有下钻关系的维度
            parent_key = random.choice(list(drill_down_relations.keys()))
            child_key = drill_down_relations[parent_key]

            parent_dimension = self._get_dimension_value(parent_key)
            child_dimension = child_key  # 使用维度名称而不是具体值
            metric = self._get_metric_value()

            question = template.format(
                parent_dimension=parent_dimension,
                child_dimension=child_dimension,
                metric=metric
            )
            questions.append(question)

        return questions

    def generate_multi_drill_down_query(self, count: int = 5) -> List[str]:
        """生成多层下钻查询问题"""
        questions = []
        templates = self.question_templates['multi_drill_down']

        # 定义多层级关系，更贴近实际业务场景
        multi_drill_relations = [
            ('群组', '用工类型', '用工模式'),  # 群组 -> 用工类型 -> 用工模式
            ('中心', '群组', '用工类型'),      # 中心 -> 群组 -> 用工类型
            ('群组', '用工类型', '业态'),      # 群组 -> 用工类型 -> 业态
            ('省份', '城市', '用工类型'),      # 省份 -> 城市 -> 用工类型
            ('业务', '用工类型', '业态'),      # 业务 -> 用工类型 -> 业态
        ]

        for _ in range(count):
            template = self._get_random_item(templates)

            # 选择一个多层关系
            parent_key, child1_key, child2_key = random.choice(multi_drill_relations)

            # 获取具体的维度值
            parent_dimension = self._get_dimension_value(parent_key)
            child_dimension1 = self._get_dimension_value(child1_key)
            child_dimension2 = child2_key  # 第二层使用维度名称

            # 获取指标
            metric = self._get_metric_value()

            # 获取时间
            time_point = self._get_time_value("time_point")
            time_range = self._get_time_value("time_range")

            # 对于需要多指标的模板，获取第二个指标
            all_metrics = []
            for category_metrics in self.config_data.get('指标', {}).values():
                if isinstance(category_metrics, list):
                    all_metrics.extend(category_metrics)

            if len(all_metrics) >= 2:
                metric1, metric2 = random.sample(all_metrics, 2)
            else:
                metric1 = metric
                metric2 = self._get_metric_value()
                while metric1 == metric2:
                    metric2 = self._get_metric_value()

            # 根据模板需要的参数进行格式化
            try:
                if "{metric1}" in template and "{metric2}" in template:
                    question = template.format(
                        parent_dimension=parent_dimension,
                        child_dimension1=child_dimension1,
                        child_dimension2=child_dimension2,
                        metric1=metric1,
                        metric2=metric2,
                        time_point=time_point,
                        time_range=time_range
                    )
                else:
                    question = template.format(
                        parent_dimension=parent_dimension,
                        child_dimension1=child_dimension1,
                        child_dimension2=child_dimension2,
                        metric=metric,
                        time_point=time_point,
                        time_range=time_range
                    )
            except KeyError:
                # 如果模板参数不匹配，使用基础模板
                question = f"{time_point}{parent_dimension}{child_dimension1}的{metric}"

            questions.append(question)

        return questions

    def generate_all_question_types(self,count_per_type: int = 3) -> Dict[str, List[str]]:
        """生成所有类型的测试问题"""
        all_questions = {
            '指标达成-无时间查询': self.generate_no_time_query(count_per_type),
            '指标达成-单时间点查询': self.generate_single_time_query(count_per_type),
            '指标达成-时间范围查询': self.generate_time_range_query(count_per_type),
            '指标达成-时间对比查询': self.generate_time_comparison_query(count_per_type),
            '指标达成-维度对比查询': self.generate_dimension_comparison_query(count_per_type),
            '指标达成-维度计算聚合': self.generate_dimension_aggregation_query(count_per_type),
            '指标达成-时间计算聚合': self.generate_time_aggregation_query(count_per_type),
            '指标达成-多指标查询': self.generate_multi_metric_query(count_per_type),
            '趋势查询-时间范围查询': self.generate_trend_time_range_query(count_per_type),
            '趋势查询-时间对比查询': self.generate_trend_time_comparison_query(count_per_type),
            '下钻查询-单层下钻': self.generate_single_drill_down_query(count_per_type),
            '下钻查询-多层下钻': self.generate_multi_drill_down_query(count_per_type)
        }

        return all_questions

    def generate_question_by_type(self,question_type,count_per_type: int = 3) -> Dict[str, List[str]]:
        """根据类型生成测试问题"""
        if question_type=='指标达成-无时间查询':
            return {question_type:self.generate_no_time_query(count_per_type)}
        elif question_type=="指标达成-单时间点查询":
            return {question_type:self.generate_single_time_query(count_per_type)}
        elif question_type=="指标达成-时间范围查询":
            return {question_type:self.generate_time_range_query(count_per_type)}
        elif question_type=="指标达成-时间对比查询":
            return {question_type:self.generate_time_comparison_query(count_per_type)}
        elif question_type=="指标达成-维度对比查询":
            return {question_type:self.generate_dimension_comparison_query(count_per_type)}
        elif question_type=="指标达成-维度计算聚合":
            return {question_type:self.generate_dimension_aggregation_query(count_per_type)}
        elif question_type=="指标达成-时间计算聚合":
            return {question_type:self.generate_time_aggregation_query(count_per_type)}
        elif question_type=="指标达成-多指标查询":
            return {question_type:self.generate_multi_metric_query(count_per_type)}
        elif question_type=="趋势查询-时间范围查询":
            return {question_type:self.generate_trend_time_range_query(count_per_type)}
        elif question_type=="趋势查询-时间对比查询":
            return {question_type:self.generate_trend_time_comparison_query(count_per_type)}
        elif question_type=="下钻查询-单层下钻":
            return {question_type:self.generate_single_drill_down_query(count_per_type)}
        elif question_type=="下钻查询-多层下钻":
            return {question_type:self.generate_multi_drill_down_query(count_per_type)}
        else:
            raise ValueError(f"未知的问题类型: {question_type}")


    def generate_questions_to_file(self, output_path: str, count_per_type: int = 5) -> None:
        """生成问题并保存到文件"""
        all_questions = self.generate_all_question_types(count_per_type)

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# 自动生成的测试问题\n\n")

            for question_type, questions in all_questions.items():
                f.write(f"## {question_type}\n\n")
                for i, question in enumerate(questions, 1):
                    f.write(f"{i}. {question}\n")
                f.write("\n")

        self.logger.info(f"问题已生成并保存到: {output_path}")

    def get_flat_question_list(self, count_per_type: int = 3) -> List[str]:
        """获取扁平化的问题列表，用于直接测试"""
        all_questions = self.generate_all_question_types(count_per_type)
        flat_list = []

        for questions in all_questions.values():
            flat_list.extend(questions)

        return flat_list

    def save_questions_to_file(self, questions_by_type: Dict[str, List[str]], output_path: str) -> None:
        """
        保存已有的问题字典到文件
        
        Args:
            questions_by_type: 按类型分组的问题字典
            output_path: 输出文件路径
        """
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# 自动生成的测试问题\n\n")

            for question_type, questions in questions_by_type.items():
                f.write(f"## {question_type}\n\n")
                for i, question in enumerate(questions, 1):
                    f.write(f"{i}. {question}\n")
                f.write("\n")

        self.logger.info(f"问题已保存到: {output_path}")
