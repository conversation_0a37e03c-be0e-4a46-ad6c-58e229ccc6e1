import logging
from typing import Dict, List, Any
import requests
import json
import time

class LLMQuestionFilter:
    """
    使用大模型筛查和优化生成的问题
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化LLM问题筛查器
        
        Args:
            config: 配置信息，包含LLM API相关配置
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        self.api_key = config.get('llm', {}).get('api_key')
        self.base_url = config.get('llm', {}).get('base_url')
        self.model_name = config.get('llm', {}).get('model_name')
        
        if not all([self.api_key, self.base_url, self.model_name]):
            self.logger.warning("LLM配置不完整，可能无法正常使用大模型筛查功能")
    
    def filter_questions(self, questions_by_type: Dict[str, List[str]], custom_prompt: str = None) -> Dict[str, List[str]]:
        """
        使用大模型筛查和优化问题
        
        Args:
            questions_by_type: 按类型分组的问题字典
            custom_prompt: 自定义提示词，为None时使用默认提示词
            
        Returns:
            Dict[str, List[str]]: 筛查优化后的问题字典
        """
        if not all([self.api_key, self.base_url, self.model_name]):
            self.logger.warning("LLM配置不完整，跳过大模型筛查")
            return questions_by_type
        
        filtered_questions = {}
        
        # 默认提示词
        default_prompt = """
        你是一个专业的问题构建专家，需要根据输入问题列表将问题改写用于测试chatbi的效果。改写要求：
        1. 语句通顺、表达清晰
        2. 符合中文语法习惯
        3. 保持问题的原始意图和类型不变
        4. 尝试不同的问题句式或者助词，使得改写后的问题类型复杂多样。更加符合人类问问题的方式。
        
        请直接返回优化后的问题列表，每行一个问题，不要添加额外解释。
        """
        
        prompt = custom_prompt if custom_prompt else default_prompt
        
        for question_type, questions in questions_by_type.items():
            self.logger.info(f"正在筛查优化 {question_type} 类型的问题...")
            
            # 构建完整提示词
            full_prompt = f"{prompt}\n\n问题类型：{question_type}\n问题列表：\n" + "\n".join(questions)
            
            try:
                # 调用LLM API
                optimized_questions = self._call_llm_api(full_prompt)
                
                # 解析返回的问题列表
                if optimized_questions:
                    # 分行并过滤空行
                    question_list = [q.strip() for q in optimized_questions.split('\n') if q.strip()]
                    
                    # 如果返回的问题数量与原问题数量不一致，进行处理
                    if len(question_list) != len(questions):
                        self.logger.warning(f"{question_type} 类型的问题数量不一致: 原 {len(questions)} vs 新 {len(question_list)}")
                        # 如果返回的问题太少，保留原问题
                        if len(question_list) < len(questions):
                            question_list.extend(questions[len(question_list):])
                        # 如果返回的问题太多，截断
                        else:
                            question_list = question_list[:len(questions)]
                    
                    filtered_questions[question_type] = question_list
                else:
                    self.logger.warning(f"未能获取 {question_type} 类型的优化问题，保留原问题")
                    filtered_questions[question_type] = questions
                    
            except Exception as e:
                self.logger.error(f"筛查 {question_type} 类型问题时出错: {str(e)}")
                filtered_questions[question_type] = questions
                
            # 避免频繁请求API
            time.sleep(0.5)
                
        return filtered_questions
    
    def _call_llm_api(self, prompt: str) -> str:
        """
        调用LLM API进行问题优化
        
        Args:
            prompt: 完整的提示词
            
        Returns:
            str: 优化后的问题文本
        """
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            data = {
                "model": self.model_name,
                "messages": [
                    {"role": "system", "content": "你是一个专业的数据分析问题优化专家。"},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,  # 使用较低的温度以获得更一致的输出
                "max_tokens": 2048
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                data=json.dumps(data),
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("choices", [{}])[0].get("message", {}).get("content", "")
            else:
                self.logger.error(f"API请求失败: {response.status_code} - {response.text}")
                return ""
                
        except Exception as e:
            self.logger.error(f"调用LLM API时出错: {str(e)}")
            return ""