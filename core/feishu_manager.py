import os
import logging
import time
import yaml
import random
from typing import List, Tuple, Optional, Dict, Any
import requests
from requests_toolbelt import MultipartEncoder
from utils.feishu_sheet import read_feishu_sheet, insert_sheet
from utils.auth import get_token

class FeishuManager:
    """
    管理与飞书表格的交互，包括读取数据和上传图片。
    """
    def __init__(self, config: dict):
        """
        初始化 FeishuManager。

        Args:
            config (dict): 包含飞书相关配置的字典。
        """
        self.config = config
        feishu_config = config.get('feishu', {})
        self.app_id = feishu_config.get('app_id')
        self.app_secret = feishu_config.get('app_secret')
        self.user_access_token = feishu_config.get('user_access_token')
        self.spreadsheet_token = feishu_config.get('spreadsheet_token')
        
        # 更新为多列配置
        self.input_row = feishu_config.get('input_row')  # 问题插入的起始行
        self.input_col = feishu_config.get('input_col')  # 问题输入列
        self.output_col = feishu_config.get('output_col')  # 问题输出列
        self.pic_col = feishu_config.get('pic_col')  # 截图列
        self.question_type_col = feishu_config.get('question_type_col')  # 问题类型列（业务指标或人力指标）
        self.test_type_col = feishu_config.get('test_type_col')  # 测试类型列
        self.subtype_col = feishu_config.get('subtype_col')  # 子类型列

        # 重试配置
        retry_config = feishu_config.get('insert_retry', {})
        self.default_max_retries = retry_config.get('max_retries', 3)
        self.default_retry_delay = retry_config.get('retry_delay', 1.0)
        self.extra_retry_delay = retry_config.get('extra_retry_delay', 2.0)
        self.enable_extra_retry = retry_config.get('enable_extra_retry', True)

        # 检查必要配置
        if not all([self.app_id, self.app_secret, self.spreadsheet_token, self.output_col]):
            raise ValueError("飞书配置信息不完整，需要app_id, app_secret, spreadsheet_token, output_col")

        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # 每次启动时刷新token
        self.logger.info("应用启动，自动刷新用户访问令牌...")
        self._refresh_user_access_token()

    def get_sheet_data(self) -> List[Tuple[int, List]]:
        """
        从飞书表格中读取指定范围的数据。

        Returns:
            List[Tuple[int, List]]: 包含行号和行数据的元组列表。

        Raises:
            ValueError: 当无法获取表格数据时抛出。
        """
        try:
            sheet_data = read_feishu_sheet(
                token=self.spreadsheet_token,
                user_access_token=self.user_access_token,
            )
            
            value_range = sheet_data.get('data', {}).get('valueRange', {})
            if not value_range.get('values'):
                self.logger.warning("未获取到表格数据")
                return []

            all_rows = value_range['values']
            start_row, end_row = self._parse_range(self.config.get('feishu', {}).get('input_row'))
            start_col, end_col = self._parse_col_range(self.config.get('feishu',{}).get('input_col'))
            
            if start_row > len(all_rows) or end_row > len(all_rows):
                raise ValueError(f"行范围超出表格范围: {start_row}-{end_row}")
            
            filtered_rows = all_rows[start_row-1:end_row]
            filtered_data = []
            for row in filtered_rows:
                if len(row) > start_col:
                    row_data = row[start_col:min(end_col + 1, len(row))]
                    filtered_data.append(row_data)
            
            original_row_numbers = range(start_row, start_row + len(filtered_data))
            return list(zip(original_row_numbers, filtered_data))

        except Exception as e:
            self.logger.error(f"获取表格数据失败: {str(e)}")
            raise

    def upload_image_and_update_sheet(self, image_path: str, row: int) -> None:
        """
        将图片上传到飞书并更新指定单元格。

        Args:
            image_path (str): 要上传的图片路径。
            row (int): 要更新的表格行号。

        Raises:
            FileNotFoundError: 当图片文件不存在时抛出。
            requests.exceptions.RequestException: 当API请求失败时抛出。
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")

        # 随机刷新token (10%概率)
        if random.random() < 0.1:
            self.logger.info("随机触发token刷新...")
            self._refresh_user_access_token()

        try:
            spreadsheet_token_val, sheet_id = self.spreadsheet_token.strip().split('_')
            cell = f"{self.pic_col}{row}"  # 使用pic_col而不是output_col
            url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{spreadsheet_token_val}/values_image"

            with open(image_path, 'rb') as f:
                image_content = f.read()

            payload = {
                "range": f"{sheet_id}!{cell}",
                "image": image_content,
                "name": os.path.basename(image_path)
            }

            headers = {
                'Authorization': f'Bearer {self.user_access_token}',
                'Content-Type': 'application/json'
            }

            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()
            self.logger.info(f"成功更新表格第 {row} 行: {cell}")

        except requests.exceptions.RequestException as e:
            self.logger.error(f"更新表格失败: {str(e)}")
            raise

    def _parse_range(self, range_str: str) -> Tuple[int, int]:
        """
        解析行或列的范围字符串。

        Args:
            range_str (str): 范围字符串，格式如 "1-10"。

        Returns:
            Tuple[int, int]: 起始值和结束值的元组。

        Raises:
            ValueError: 当范围格式无效时抛出。
        """
        try:
            parts = range_str.split('-')
            start = int(parts[0])
            end = int(parts[1]) if len(parts) > 1 else start
            if start > end:
                raise ValueError(f"无效的范围: {start} > {end}")
            return start, end
        except (ValueError, IndexError) as e:
            raise ValueError(f"无效的范围格式 '{range_str}': {str(e)}")

    def _parse_col_range(self, col_range_str: str) -> Tuple[int, int]:
        """
        解析列范围字符串 (例如 'A-C')。

        Args:
            col_range_str (str): 列范围字符串。

        Returns:
            Tuple[int, int]: 起始列索引和结束列索引的元组。

        Raises:
            ValueError: 当列范围格式无效时抛出。
        """
        try:
            parts = col_range_str.upper().split('-')
            if not all(c.isalpha() for c in ''.join(parts)):
                raise ValueError("列标识必须是字母")
                
            start_col = ord(parts[0]) - ord('A')
            end_col = ord(parts[1]) - ord('A') if len(parts) > 1 else start_col
            
            if start_col > end_col:
                raise ValueError(f"无效的列范围: {parts[0]} > {parts[1]}")
                
            return start_col, end_col
        except (ValueError, IndexError) as e:
            raise ValueError(f"无效的列范围格式 '{col_range_str}': {str(e)}")

    def insert_questions_to_sheet(self, questions: List[str], start_row: int = None,
                                 target_col: str = None, question_type: str = None,
                                 test_type: str = None, subtype: str = None,
                                 max_retries: int = None, retry_delay: float = None) -> Dict[str, Any]:
        """
        将生成的问题批量插入到飞书表格中，支持重试机制

        Args:
            questions (List[str]): 要插入的问题列表
            start_row (int, optional): 开始插入的行号，如果为None则自动查找空行
            target_col (str, optional): 目标列，如果为None则使用output_col配置
            question_type (str, optional): 问题类型（业务指标或人力指标）
            test_type (str, optional): 测试类型
            subtype (str, optional): 子类型
            max_retries (int): 每个问题的最大重试次数，默认为3次
            retry_delay (float): 重试间隔时间（秒），默认为1.0秒

        Returns:
            Dict[str, Any]: 插入结果信息

        Raises:
            ValueError: 当配置信息不完整时抛出
            requests.exceptions.RequestException: 当API请求失败时抛出
        """
        if not questions:
            self.logger.warning("问题列表为空，无需插入")
            return {"success": True, "inserted_count": 0, "message": "问题列表为空"}

        # 使用配置中的默认值
        if max_retries is None:
            max_retries = self.default_max_retries
        if retry_delay is None:
            retry_delay = self.default_retry_delay

        # 随机刷新token (10%概率)
        if random.random() < 0.1:
            self.logger.info("随机触发token刷新...")
            self._refresh_user_access_token()

        # 确定目标列
        if target_col is None:
            target_col = self.output_col

        # 确定开始行号
        if start_row is None:
            start_row = self.input_row

        success_count = 0
        failed_questions = []

        try:
            for i, question in enumerate(questions):
                current_row = start_row + i

                # 插入类型信息（如果提供）
                if question_type and self.question_type_col:
                    try:
                        insert_sheet(
                            sheet_token=self.spreadsheet_token,
                            position=f"{self.question_type_col}{current_row}",
                            value=question_type,
                            user_token=self.user_access_token
                        )
                    except Exception as e:
                        self.logger.warning(f"插入问题类型到 {self.question_type_col}{current_row} 失败: {str(e)}")

                if test_type and self.test_type_col:
                    try:
                        insert_sheet(
                            sheet_token=self.spreadsheet_token,
                            position=f"{self.test_type_col}{current_row}",
                            value=test_type,
                            user_token=self.user_access_token
                        )
                    except Exception as e:
                        self.logger.warning(f"插入测试类型到 {self.test_type_col}{current_row} 失败: {str(e)}")

                if subtype and self.subtype_col:
                    try:
                        insert_sheet(
                            sheet_token=self.spreadsheet_token,
                            position=f"{self.subtype_col}{current_row}",
                            value=subtype,
                            user_token=self.user_access_token
                        )
                    except Exception as e:
                        self.logger.warning(f"插入子类型到 {self.subtype_col}{current_row} 失败: {str(e)}")

                # 插入问题（主要内容，使用更详细的重试逻辑）
                position = f"{target_col}{current_row}"
                question_inserted = False

                try:
                    insert_sheet(
                        sheet_token=self.spreadsheet_token,
                        position=position,
                        value=question,
                        user_token=self.user_access_token,
                    )
                    question_inserted = True
                    success_count += 1
                    self.logger.info(f"成功插入问题到 {position}: {question[:50]}...")

                except Exception as e:
                    self.logger.error(f"插入问题到 {position} 失败（已重试{max_retries}次）: {str(e)}")
                    failed_questions.append({
                        "question": question,
                        "position": position,
                        "error": str(e),
                        "retry_count": max_retries
                    })

                # 如果问题插入失败，记录详细信息用于后续重试
                if not question_inserted:
                    self.logger.warning(f"问题插入失败，将在结果中标记: {question[:50]}...")

            result = {
                "success": success_count > 0,
                "inserted_count": success_count,
                "failed_count": len(failed_questions),
                "total_count": len(questions),
                "start_row": start_row,
                "end_row": start_row + len(questions) - 1,
                "target_col": target_col,
                "failed_questions": failed_questions
            }

            self.logger.info(f"问题插入完成: 成功 {success_count}/{len(questions)} 个")

            return result

        except Exception as e:
            self.logger.error(f"批量插入问题失败: {str(e)}")
            raise

    def insert_generated_questions_by_type(self, questions_by_type: Dict[str, List[str]],
                                          start_row: int = None, target_col: str = None,
                                          max_retries: int = None, retry_delay: float = None) -> Dict[str, Any]:
        """
        按类型插入生成的问题到飞书表格，不添加类型标题行和空行，支持重试机制

        Args:
            questions_by_type (Dict[str, List[str]]): 按类型分组的问题字典
            start_row (int, optional): 开始插入的行号
            target_col (str, optional): 目标列
            max_retries (int): 每个问题的最大重试次数，默认为3次
            retry_delay (float): 重试间隔时间（秒），默认为1.0秒

        Returns:
            Dict[str, Any]: 插入结果信息
        """
        if not questions_by_type:
            self.logger.warning("问题字典为空，无需插入")
            return {"success": True, "inserted_count": 0, "message": "问题字典为空"}

        # 使用配置中的默认值
        if max_retries is None:
            max_retries = self.default_max_retries
        if retry_delay is None:
            retry_delay = self.default_retry_delay

        # 随机刷新token (10%概率)
        if random.random() < 0.1:
            self.logger.info("随机触发token刷新...")
            self._refresh_user_access_token()

        if random.random() < 0.1:
            time.sleep(1)


        # 确定目标列
        if target_col is None:
            target_col = self.output_col

        # 确定开始行号
        if start_row is None:
            start_row = self.input_row

        self.logger.info(f"开始按类型向飞书表格插入问题，从第 {start_row} 行开始")

        current_row = start_row
        total_success = 0
        total_failed = 0
        type_results = {}

        try:
            for question_type, questions in questions_by_type.items():
                if not questions:
                    continue

                # 解析问题类型信息
                type_parts = question_type.split('-')
                main_type = type_parts[0].strip() if len(type_parts) > 0 else ""  # 主类型（业务指标或人力指标）
                test_type = type_parts[0].strip() if len(type_parts) <= 1 else type_parts[1].strip()  # 测试类型
                subtype = "" if len(type_parts) <= 2 else type_parts[2].strip()  # 子类型
                
                # 直接插入问题，不添加类型标题行
                # 批量插入问题，不添加序号
                result = self.insert_questions_to_sheet(
                    questions=questions,  # 直接使用原始问题，不添加序号
                    start_row=current_row,
                    target_col=target_col,
                    question_type=main_type,
                    test_type=test_type,
                    subtype=subtype,
                    max_retries=max_retries,
                    retry_delay=retry_delay
                )

                type_results[question_type] = result
                total_success += result.get('inserted_count', 0)
                total_failed += result.get('failed_count', 0)

                # 更新当前行号（只考虑问题行，不添加空行）
                current_row += len(questions)

            final_result = {
                "success": total_success > 0,
                "total_inserted": total_success,
                "total_failed": total_failed,
                "start_row": start_row,
                "end_row": current_row - 1,
                "target_col": target_col,
                "type_results": type_results
            }

            self.logger.info(f"按类型插入问题完成: 总共成功 {total_success} 个，失败 {total_failed} 个")

            return final_result

        except Exception as e:
            self.logger.error(f"按类型插入问题失败: {str(e)}")
            raise

    def retry_failed_questions(self, failed_questions: List[Dict[str, Any]],
                              max_retries: int = 3, retry_delay: float = 2.0) -> Dict[str, Any]:
        """
        重试之前失败的问题插入

        Args:
            failed_questions (List[Dict[str, Any]]): 失败的问题列表，每个元素包含question, position, error等信息
            max_retries (int): 最大重试次数，默认为3次
            retry_delay (float): 重试间隔时间（秒），默认为2.0秒

        Returns:
            Dict[str, Any]: 重试结果信息
        """
        if not failed_questions:
            self.logger.info("没有失败的问题需要重试")
            return {"success": True, "retry_success_count": 0, "retry_failed_count": 0, "message": "没有失败的问题"}

        self.logger.info(f"开始重试 {len(failed_questions)} 个失败的问题...")

        retry_success_count = 0
        retry_failed_count = 0
        retry_failed_questions = []

        for failed_item in failed_questions:
            question = failed_item.get("question", "")
            position = failed_item.get("position", "")

            if not question or not position:
                self.logger.warning(f"跳过无效的失败问题项: {failed_item}")
                retry_failed_count += 1
                continue

            try:
                self.logger.info(f"重试插入问题到 {position}: {question[:50]}...")

                insert_sheet(
                    sheet_token=self.spreadsheet_token,
                    position=position,
                    value=question,
                    user_token=self.user_access_token
                )

                retry_success_count += 1
                self.logger.info(f"重试成功: {position}")

            except Exception as e:
                retry_failed_count += 1
                self.logger.error(f"重试失败 {position}: {str(e)}")
                retry_failed_questions.append({
                    "question": question,
                    "position": position,
                    "error": str(e),
                    "original_error": failed_item.get("error", ""),
                    "retry_count": max_retries
                })

        result = {
            "success": retry_success_count > 0,
            "retry_success_count": retry_success_count,
            "retry_failed_count": retry_failed_count,
            "total_retry_count": len(failed_questions),
            "retry_failed_questions": retry_failed_questions
        }

        self.logger.info(f"重试完成: 成功 {retry_success_count}/{len(failed_questions)} 个")
        return result

    def _refresh_user_access_token(self) -> bool:
        """
        使用get_token函数获取新的用户访问令牌并更新配置文件

        Returns:
            bool: 是否成功获取并更新token
        """
        try:
            self.logger.info("正在获取新的用户访问令牌...")

            # 使用get_token函数获取访问令牌
            new_token = get_token(self.app_id, self.app_secret)

            if new_token:
                # 更新内存中的token
                self.user_access_token = new_token

                return True
            else:
                self.logger.error("获取新的用户访问令牌失败")
                return False

        except Exception as e:
            self.logger.error(f"刷新用户访问令牌失败: {str(e)}")
            return False