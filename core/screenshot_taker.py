import asyncio
import aiohttp
import json
import base64
import os
from typing import Optional
import logging
class ScreenshotTaker:
    """
    负责使用 Playwright 截取网页截图。
    """
    def __init__(self, config: dict):
        """
        初始化 ScreenshotTaker。

        Args:
            config (dict): 包含配置信息的字典，如 'url' 和 'headless'。
        """
        self.config = config
        self.browser = None
        self.page = None
        self.playwright = None
        self.logger = logging.getLogger(__name__)

    async def __aenter__(self):
        """
        异步上下文管理器入口，用于创建mcp浏览器沙箱会话并导航到目标URL。
        """
        try:
            self.mcp_url = self.config.get('tars_agent', {}).get('url')
            if not self.mcp_url:
                self.logger.warning('配置中缺少 tars_agent.url 字段，跳过截图功能')
                return self

            # 创建mcp会话
            async with aiohttp.ClientSession() as session:
                async with session.post(f'{self.mcp_url}/create_session', json={
                    'allow_url_list': ['https://cqc-tool-c-bot.gf-boe.bytedance.net/*']
                }) as response:
                    result = await response.json()
                    self.session_id = result.get('session_id')
                    if not self.session_id:
                        raise ValueError('创建mcp会话失败')

                # 导航到目标URL
                url = self.config.get('url')
                if not url:
                    raise ValueError('配置中缺少 url 字段或 url 为空')
                await session.post(f'{self.mcp_url}/browser_navigate', json={
                    'session_id': self.session_id,
                    'url': url
                })

            return self
        except Exception as e:
            self.logger.error(f"浏览器启动失败: {str(e)}")
            await self.__aexit__(None, None, None)
            raise

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """
        异步上下文管理器出口，用于释放mcp浏览器沙箱会话。
        """
        try:
            if self.session_id:
                async with aiohttp.ClientSession() as session:
                    await session.post(f'{self.mcp_url}/release_session', json={
                        'session_id': self.session_id
                    })
        except Exception as e:
            self.logger.error(f"释放mcp会话失败: {str(e)}")

    async def take_screenshot(self, question: str, screenshot_path: str, retry_count: int = 3) -> bool:
        """
        导航到指定URL，输入问题，等待响应，并截取屏幕截图。

        Args:
            question (str): 要在页面上输入的问题。
            screenshot_path (str): 截图保存的路径。
            retry_count (int): 重试次数。

        Returns:
            bool: 截图是否成功。
        """
        if not hasattr(self, 'mcp_url') or not self.mcp_url:
            self.logger.warning("跳过截图：缺少 tars_agent 配置")
            return False

        for attempt in range(retry_count):
            try:
                # 确保目标目录存在
                os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
                
                # 输入初始问候语
                await self._input_text('你好')
                await asyncio.sleep(2)  # 等待页面响应
                
                # 输入实际问题
                await self._input_text(question)
                
                # 等待响应完成的标志
                async with aiohttp.ClientSession() as session:
                    await session.post(f'{self.mcp_url}/browser_wait_for', json={{
                        'session_id': self.session_id,
                        'text': '重新生成',
                        'time': 60  # 等待时间（秒）
                    }})

                    # 截图
                    async with session.post(f'{self.mcp_url}/browser_take_screenshot', json={{
                        'session_id': self.session_id
                    }}) as response:
                        result = await response.json()
                        base64_image = result.get('screenshot')
                        if not base64_image:
                            raise ValueError('获取截图失败')
                        
                        # 保存截图
                        with open(screenshot_path, 'wb') as f:
                            f.write(base64.b64decode(base64_image))
                
                self.logger.info(f"截图成功保存至: {screenshot_path}")
                return True

            except Exception as e:
                self.logger.error(f"第 {attempt + 1} 次截图尝试失败: {str(e)}")
                if attempt == retry_count - 1:
                    self.logger.error(f"截图最终失败，问题: '{question}'")
                    return False
                await asyncio.sleep(2)  # 重试前等待

    async def _input_text(self, text: str) -> None:
        """
        在文本框中输入文字并提交。

        Args:
            text (str): 要输入的文字。
        """
        # 使用mcp-browser-sandbox输入文本并提交
        script = f"""
        const textarea = document.querySelector('textarea');
        textarea.value = {json.dumps(text)};
        textarea.dispatchEvent(new KeyboardEvent('keypress', {{ key: 'Enter', code: 'Enter' }}));
        """
        async with aiohttp.ClientSession() as session:
            await session.post(f'{self.mcp_url}/browser_evaluate', json={{
                'session_id': self.session_id,
                'script': script
            }})
