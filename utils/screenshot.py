import os
import time
from playwright.sync_api import sync_playwright
from requests_toolbelt import MultipartEncoder
import requests

class ScreenshotUploader:
    def __init__(self, user_access_token, output_col, spreadsheet_token, screenshot_dir):
        self.user_access_token = user_access_token
        self.output_col = output_col
        self.spreadsheet_token = spreadsheet_token
        self.screenshot_dir = screenshot_dir
        os.makedirs(self.screenshot_dir, exist_ok=True)

    def take_screenshot(self, question, idx):
        safe_question = "".join([c for c in question if c.isalnum() or c in ' _-']).strip()
        screenshot_path = os.path.join(self.screenshot_dir, f'{safe_question}.png')
        with sync_playwright() as playwright:
            context = playwright.chromium.launch_persistent_context(
                user_data_dir="./user_data",
                headless=False
            )
            page = context.pages[0] if context.pages else context.new_page()
            if not context.pages:
                page.goto('https://cqc-tool-c-bot.gf-boe.bytedance.net/login')
                page.wait_for_url('https://cqc-tool-c-bot.gf-boe.bytedance.net/home')
            page.goto('https://cqc-tool-c-bot.gf-boe.bytedance.net/home')
            input_box = page.get_by_role("textbox")
            input_box.fill("")
            input_box.fill(question)
            input_box.press("Enter")
            print(f"问题已提交: {question[:30]}... 等待10秒后截图")
            time.sleep(10)
            page.screenshot(path=screenshot_path)
            print(f"已完成问题 {idx}: {question[:30]}... 截图保存至: {screenshot_path}")
            context.close()
        return screenshot_path

    def upload_image_and_update_sheet(self, image_path, row):
        upload_url = "https://open.feishu.cn/open-apis/im/v1/images"
        multi_form = MultipartEncoder(
            fields={
                'image_type': 'message',
                'image': (os.path.basename(image_path), open(image_path, 'rb'), 'image/png')
            }
        )
        headers = {
            'Authorization': f'Bearer {self.user_access_token}',
            'Content-Type': multi_form.content_type
        }
        response = requests.post(upload_url, headers=headers, data=multi_form)
        response.raise_for_status()
        result = response.json()
        image_key = result['data']['image_key']
        position = f"{self.output_col}{row}"
        spreadsheet_token = self.spreadsheet_token.strip().split('_')[0]
        sheet_id = self.spreadsheet_token.strip().split('_')[1]
        url = 'https://fsopen.bytedance.net/open-apis/sheets/v3/spreadsheets/{0}/sheets/{1}/values/batch_update?user_id_type=user_id'.format(spreadsheet_token, sheet_id)
        headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer {0}'.format(self.user_access_token)
        }
        payload = {
            "value_ranges": [
                {
                    "range": f"{sheet_id}!{position}:{position}",
                    "values": [
                        [
                            {
                                "fileToken": image_key,
                                "type": "embed-image"
                            }
                        ]
                    ]
                }
            ]
        }
        response = requests.post(url, headers=headers, json=payload)
        print(f"Status Code: {response.status_code}")
        print(f"Response Body: {response.text}")
        print(f"已更新表格第{row}行: {position}")

