import requests
import json
import os
from typing import Optional
from pathlib import Path


# 获取访问令牌
def get_token(app_id: str, app_secret: str) -> str:
    """
    获取飞书访问令牌
    
    Args:
        app_id: 应用ID
        app_secret: 应用密钥
    
    Returns:
        str: 访问令牌
    """
   
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
    payload = {
        "app_id": app_id,
        "app_secret": app_secret
    }
    
    try:
        # 发送POST请求
        response = requests.post(url, json=payload)
        # 如果请求失败，则抛出HTTPError异常
        response.raise_for_status()
        # 返回租户访问令牌
        return response.json()['tenant_access_token']
    except requests.exceptions.RequestException as e:
        # 捕获请求异常并抛出自定义异常
        raise Exception(f"获取访问令牌失败: {str(e)}")


