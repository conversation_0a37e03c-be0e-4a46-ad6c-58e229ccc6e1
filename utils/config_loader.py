import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
import logging
from functools import lru_cache
logger = logging.getLogger(__name__)

class ConfigLoader:
    """配置加载器,实现单例模式,支持分环境配置"""
    
    _instance: Optional['ConfigLoader'] = None
    _config: Optional[Dict[str, Any]] = None
    def __init__(self, config_path: str):
        """
        初始化配置加载器
        
        Args:
            config_path (str): 配置文件路径
        """
        if not hasattr(self, 'config_path'):
            self.config_path = Path(config_path)
            self._config = self._load_config()

    @property
    def project_root(self) -> Path:
        """获取项目根目录"""
        return Path(__file__).parent.parent

    @lru_cache()
    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件,合并基础配置和环境特定配置。
        
        Returns:
            Dict[str, Any]: 合并后的配置字典。
        """
        env = os.getenv('ENV', 'dev')
        
        # 加载基础配置
        base_config_path = self.project_root / 'config' / 'config.yaml'
        config = self._load_yaml_file(base_config_path, "基础配置")
        
        # 加载并合并环境特定配置
        env_config_path = self.project_root / 'config' / f'config.{env}.yaml'
        if env_config_path.exists():
            env_config = self._load_yaml_file(env_config_path, f"{env}环境配置")
            if env_config:
                config = self._merge_configs(config, env_config)
        
        return config

    def _load_yaml_file(self, file_path: Path, config_type: str) -> Dict[str, Any]:
        """
        从指定路径加载YAML文件。
        
        Args:
            file_path (Path): YAML文件的路径。
            config_type (str): 配置类型描述 (例如, "基础配置")。
            
        Returns:
            Dict[str, Any]: 加载的配置字典，如果文件不存在或加载失败则为空字典。
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except FileNotFoundError:
            logger.warning(f"未找到{config_type}文件: {file_path}")
        except yaml.YAMLError as e:
            logger.error(f"解析{config_type}文件失败: {file_path}, 错误: {e}")
        except Exception as e:
            logger.error(f"加载{config_type}文件失败: {file_path}, 错误: {e}")
        return {}
    
    def _merge_configs(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """
        递归合并两个配置字典，返回新的合并后的字典。
        
        Args:
            base (Dict[str, Any]): 基础配置字典
            override (Dict[str, Any]): 覆盖配置字典
            
        Returns:
            Dict[str, Any]: 合并后的新字典
        """
        result = base.copy()
        
        for key, value in override.items():
            if isinstance(value, dict) and isinstance(result.get(key), dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
                
        return result

    def get(self, key: str, default: Any = None) -> Any:
        """
        根据键获取配置值，支持点号分隔的多级访问。
        
        Args:
            key (str): 配置键, 支持点号分隔 (例如, 'feishu.app_id')。
            default (Any, optional): 如果键不存在，则返回默认值。默认为 None。
            
        Returns:
            Any: 找到的配置值或默认值。
        """
        if not self._config or not key:
            return default
            
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if not isinstance(value, dict):
                return default
            value = value.get(k)
            if value is None:
                return default
                
        return value

# 创建全局配置加载器实例
config = ConfigLoader('../config/config.yaml')
