import requests
from pathlib import Path
import lark_oapi as lark
import yaml
from lark_oapi.api.sheets.v3 import *
from utils.auth import get_token
import pandas as pd
from utils.config_loader import config

import lark_oapi as lark
import json
from requests_toolbelt import MultipartEncoder
import json
from lark_oapi.api.docx.v1 import sheet
import time
def read_feishu_sheet(token: str, app_access_token: str) -> dict:
    """
    使用应用访问令牌 (app_access_token) 从飞书表格读取数据。

    Args:
        token (str): 飞书表格的 token，格式为 'spreadsheetToken_sheetId'。
        app_access_token (str): 应用的访问令牌。

    Returns:
        dict: 包含表格数据的字典，格式为飞书开放平台API返回的JSON。
    """
    sheet_file_token = token.strip().split('_')[0]
    sheet_token= token.strip().split('_')[1]
    # 构建请求URL和参数
    
    url = f'https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{sheet_file_token}/values/{sheet_token}'

    print(url)
    params = {
        'valueRenderOption': 'ToString',
        'dateTimeRenderOption': 'FormattedString'
    }
    headers = {
        'Authorization': f'Bearer {app_access_token}'
    }

    response = requests.get(url, params=params, headers=headers)
    return response.json()

def insert_sheet(sheet_token: str, position: str, value: str, user_token: str,retry_count=5) -> str:
    """
    向飞书表格的指定位置插入文本数据，支持重试机制。

    Args:
        sheet_token (str): 飞书表格的 token，格式为 'spreadsheetToken_sheetId'。
        position (str): 要插入数据的位置，例如 'A1'。
        value (str): 要插入的文本值。
        user_token (str): 用户的访问令牌。
        max_retries (int): 最大重试次数，默认为3次。
        retry_delay (float): 重试间隔时间（秒），默认为1.0秒。

    Returns:
        str: 请求响应的文本内容。

    Raises:
        requests.exceptions.RequestException: 当所有重试都失败时抛出最后一次的异常。
    """

    while retry_count>0:
        spreadsheet_token = sheet_token.strip().split('_')[0]
        sheet_id = sheet_token.strip().split('_')[1]
        # 目标 URL
        url = f'https://fsopen.bytedance.net/open-apis/sheets/v3/spreadsheets/{spreadsheet_token}/sheets/{sheet_id}/values/batch_update?user_id_type=user_id'

        # 请求头
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {user_token}'
        }

        #请求体数据
        payload = {
            "value_ranges": [
                {
                    "range": f"{sheet_id}!{position}:{position}",
                    "values": [
                        [
                            [
                                {
                                    "text": {
                                        "text": value,
                                    },
                                    "type": "text"
                                }
                            ]
                        ]
                    ]
                }
            ]
        }

        # 发送 POST 请求
        response = requests.post(url, headers=headers, json=payload)

        # 输出响应状态码和内容
        #print(f"Status Code: {response.status_code}")
        #print(f"Response Body: {response.text}")
        if response.status_code==200:
            return response.text
        else:
            retry_count-=1
            time.sleep(5)
    return None
    